'use client';

import React, { useEffect, useState } from 'react';
import DetailsCard from "@/components/services/DetailsCard";
import pbclient from '@/lib/db';
import { Download, FileText, Image as ImageIcon, File } from 'lucide-react';
import { useCollection } from '@/hooks/useCollection';

export default function CFSOrderView({ orderId }) {
  const { data: orders, isLoading } = useCollection('cfs_orders', {
    expand: 'containers,cfs',
    filter: `id="${orderId}"`,
  });
  const [files, setFiles] = useState([]);

  // Helper function to determine file type based on extension
  const getFileType = (filename) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const documentExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx'];

    if (imageExtensions.includes(extension)) return 'image';
    if (documentExtensions.includes(extension)) return 'document';
    return 'other';
  };

  // Helper function to get file icon
  const getFileIcon = (filename) => {
    const fileType = getFileType(filename);
    switch (fileType) {
      case 'image': return ImageIcon;
      case 'document': return FileText;
      default: return File;
    }
  };

  // Helper function to download file
  const downloadFile = (filename) => {
    if (!order) return;

    const url = pbclient.files.getURL(order, filename);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    // confirming the Orders Array and setting up files Array
    if (orders && Array.isArray(orders) && orders.length > 0) {
      const order = orders?.[0];
      if (order.files && order.files.length > 0) {
        const fileObjects = order.files.map(filename => ({
          filename,
          url: pbclient.files.getURL(order, filename),
          type: getFileType(filename)
        }));
        setFiles(fileObjects);
      }
    } else {
      console.log('Error getting files from server');
    }
  }, [orders]);

  // Once again verifing The Orders Array is comming with single index
  const order = orders?.[0];

  // Loading State
  if (isLoading)
    return <div className="p-8 text-[color:var(--secondary)] text-center">Loading order details...</div>;
  if (!order)
    return <div className="p-8 text-red-600 text-center">Order not found.</div>;

  return (
    <div className="max-w-container mx-auto px-6 py-10 space-y-8 bg-[color:var(--accent)] rounded-lg shadow-lg">
      <header className="border-b border-[color:var(--secondary)] pb-4 mb-6">
        <h1 className="text-3xl font-bold text-[color:var(--foreground)]">Order Details</h1>
        <p className="text-sm text-[color:var(--secondary)]">
          Order ID: <span className="font-mono text-[color:var(--primary)]">{order.id}</span>
        </p>
      </header>

      {/* Order Summary */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DetailsCard label="IGM No" value={order.igmNo} />
        <DetailsCard label="BL No" value={order.blNo} />
        <DetailsCard label="BOE No" value={order.boeNo} />
        <DetailsCard label="Consignee Name" value={order.consigneeName} />
        <DetailsCard label="CHA Name" value={order.chaName} />
        <DetailsCard label="Status" value={order.status} color="primary" status={true} />
        {/*
        <DetailCard label="From Date" value={new Date(order.fromDate).toLocaleDateString('en-US', { day: "2-digit", month: "long", year: "numeric" })} />
        <DetailCard label="To Date" value={new Date(order.toDate).toLocaleDateString('en-US', { day: "2-digit", month: "long", year: "numeric" })} />
			*/}
        <DetailsCard label="Description" value={order.orderDescription} full />
      </section>

      {/* CFS Info */}
      <section>
        <h2 className="text-2xl font-semibold text-[color:var(--foreground)] mb-4">CFS Information</h2>
        {order.expand?.cfs ? (
          <div className="bg-[color:var(--background-2)] p-5 rounded-md shadow-sm border border-[color:var(--primary)] space-y-2">
            <DetailsCard label="Title" value={order.expand.cfs.title} />
            <DetailsCard label="Location" value={order.expand.cfs.location} />
            <DetailsCard label="Contact" value={order.expand.cfs.contact} />
          </div>
        ) : (
          <p className="text-[color:var(--secondary)]">No CFS information available.</p>
        )}
      </section>

      {/* Containers */}
      <section>
        <h2 className="text-2xl font-semibold text-[color:var(--foreground)] mb-4">Containers</h2>
        {Array.isArray(order.expand?.containers) && order.expand.containers.length > 0 ? (
          <div className="grid md:grid-cols-2 gap-4">
            {order.expand.containers.map((container) => (
              <div
                key={container.id}
                className="border border-[color:var(--primary)] p-4 rounded-lg shadow-sm bg-white hover:bg-[color:var(--accent)] transition"
              >
                <p className="font-semibold text-[color:var(--foreground)]">
                  Container No: <span className="text-[color:var(--light-primary)]">{container.containerNo}</span>
                </p>
                <p className="text-sm text-[color:var(--secondary)]">Size: {container.size}</p>
                <p className="text-sm text-[color:var(--secondary)]">Status: {container.status}</p>
                <p className="text-sm text-[color:var(--secondary)]">Cargo Type: {container.cargoType}</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-[color:var(--secondary)]">No container data found.</p>
        )}
      </section>

      {/* Files Section */}
      {Array.isArray(files) && files.length > 0 && (
        <section>
          <h2 className="text-2xl font-semibold text-[color:var(--foreground)] mb-4">Attached Files</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {files.map((file, index) => {
              const IconComponent = getFileIcon(file.filename);

              return (
                <div
                  key={index}
                  className="group relative overflow-hidden rounded-lg shadow-md border border-[color:var(--primary)] bg-white"
                >
                  {file.type === 'image' ? (
                    // Display images normally
                    <div className="relative">
                      <img
                        src={file.url}
                        alt={file.filename}
                        className="h-40 w-full object-cover transition-transform duration-300 ease-in-out group-hover:scale-105"
                      />
                      <div className="absolute bottom-0 w-full h-full bg-background-2 text-foreground text-sm text-center py-1" />
                    </div>
                  ) : (
                    // Display documents and other files with download button
                    <div className="h-40 flex flex-col items-center justify-center p-4 text-center">
                      <IconComponent
                        size={48}
                        className="text-[color:var(--primary)] mb-2"
                      />
                      <p className="text-sm text-[color:var(--foreground)] mb-2 truncate w-full" title={file.filename}>
                        {file.filename}
                      </p>
                      <button
                        onClick={() => downloadFile(file.filename)}
                        className="flex items-center gap-2 px-3 py-1 bg-[color:var(--primary)] text-white rounded-md hover:bg-[color:var(--primary)]/80 transition-colors text-sm"
                      >
                        <Download size={16} />
                        Download
                      </button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </section>
      )}
    </div>
  );
}

